import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_absolute_error
from keras.models import Sequential
from keras.layers import LSTM, Dense, Dropout, Activation
from keras.optimizers import Adam
import pickle

plt.rcParams['font.sans-serif'] = ['SimHei']

# 1. 读取处理后的数据（包含速度和方向）
data = pd.read_csv('../Processed/Data/001/Trajectory/20081024234405.csv', skiprows=1, header=None)
data.columns = ['lat', 'lon', 'speed', 'angle']

# 使用所有4个特征：经纬度 + 速度 + 方向
print(f'样本数：{len(data)}, 维度：{data.shape[1]}')
print(f'数据概览:\n{data.head()}')

# 2. 数据预处理
# 对经纬度使用MinMaxScaler
position_scaler = MinMaxScaler()
position_scaled = position_scaler.fit_transform(data[['lat', 'lon']])

# 对速度使用StandardScaler
speed_scaler = StandardScaler()
speed_scaled = speed_scaler.fit_transform(data[['speed']])

# 对角度进行特殊处理（转换为sin和cos分量）
angles_rad = np.radians(data['angle'].values)
angle_sin = np.sin(angles_rad).reshape(-1, 1)
angle_cos = np.cos(angles_rad).reshape(-1, 1)

# 合并所有特征：lat, lon, speed, sin(angle), cos(angle)
data_scaled = np.column_stack([position_scaled, speed_scaled, angle_sin, angle_cos])
print(f'预处理后特征数：{data_scaled.shape[1]}')

# 3. 构造数据集
def create_dataset(dataset, time_step=5):
    X, Y = [], []
    for i in range(len(dataset) - time_step):
        X.append(dataset[i:(i + time_step), :])
        # 只预测位置和速度，不预测角度的sin/cos分量
        Y.append(dataset[i + time_step, :3])  # lat, lon, speed
    return np.array(X), np.array(Y)

time_step = 5
X, Y = create_dataset(data_scaled, time_step)

# 4. 划分训练集和测试集
train_size = int(len(X) * 0.8)
trainX, trainY = X[:train_size], Y[:train_size]
testX, testY = X[train_size:], Y[train_size:]
print('Train X shape:', trainX.shape)
print('Train Y shape:', trainY.shape)
print('Test X shape:', testX.shape)
print('Test Y shape:', testY.shape)

# 5. 增强LSTM模型搭建
model = Sequential()
model.add(LSTM(units=128, return_sequences=True, input_shape=(time_step, 5)))  # 5个特征
model.add(Dropout(0.2))
model.add(LSTM(units=128, return_sequences=True))
model.add(Dropout(0.2))
model.add(LSTM(units=64))
model.add(Dropout(0.2))
model.add(Dense(units=32, activation='relu'))
model.add(Dense(units=3))  # 输出3个特征：lat, lon, speed
model.add(Activation('linear'))

model.compile(loss='mse', optimizer=Adam(learning_rate=0.001), metrics=['mae'])
model.summary()

# 6. 模型训练
history = model.fit(trainX, trainY, epochs=100, batch_size=64, validation_split=0.2)

# 7. 模型评估
test_loss, test_mae = model.evaluate(testX, testY)
print(f'Test Loss: {test_loss:.4f}, Test MAE: {test_mae:.4f}')

# 8. 预测 & 反归一化
predicted = model.predict(testX)

# 分别反归一化不同特征
predicted_positions = position_scaler.inverse_transform(predicted[:, :2])
predicted_speeds = speed_scaler.inverse_transform(predicted[:, 2:3])
predicted_inverse = np.column_stack([predicted_positions, predicted_speeds])

testY_positions = position_scaler.inverse_transform(testY[:, :2])
testY_speeds = speed_scaler.inverse_transform(testY[:, 2:3])
testY_inverse = np.column_stack([testY_positions, testY_speeds])

# 9. 可视化
plt.figure(figsize=(15, 10))

# 轨迹对比
plt.subplot(2, 3, 1)
plt.plot(testY_inverse[:, 1], testY_inverse[:, 0], label='真实轨迹', marker='o', markersize=2, linestyle='-')
plt.plot(predicted_inverse[:, 1], predicted_inverse[:, 0], label='预测轨迹', marker='x', markersize=2, linestyle='--')
plt.legend()
plt.title('真实轨迹 vs 预测轨迹')
plt.xlabel('经度')
plt.ylabel('纬度')

# 位置误差
plt.subplot(2, 3, 2)
position_error = np.sqrt(np.sum((testY_inverse[:, :2] - predicted_inverse[:, :2]) ** 2, axis=1))
plt.plot(position_error)
plt.title('位置误差（欧氏距离）')
plt.xlabel('时间步')
plt.ylabel('误差')

# 速度对比
plt.subplot(2, 3, 3)
plt.plot(testY_inverse[:, 2], label='真实速度', alpha=0.8)
plt.plot(predicted_inverse[:, 2], label='预测速度', alpha=0.8)
plt.legend()
plt.title('速度预测对比')
plt.xlabel('时间步')
plt.ylabel('速度 (m/s)')

# 训练损失
plt.subplot(2, 3, 4)
plt.plot(history.history['loss'], label='训练损失')
plt.plot(history.history['val_loss'], label='验证损失')
plt.legend()
plt.title('训练损失')
plt.xlabel('Epochs')
plt.ylabel('Loss')

# MAE
plt.subplot(2, 3, 5)
plt.plot(history.history['mae'], label='训练MAE')
plt.plot(history.history['val_mae'], label='验证MAE')
plt.legend()
plt.title('MAE变化')
plt.xlabel('Epochs')
plt.ylabel('MAE')

# 误差分布
plt.subplot(2, 3, 6)
plt.hist(position_error, bins=30, alpha=0.7)
plt.title('位置误差分布')
plt.xlabel('欧氏距离误差')
plt.ylabel('频次')

plt.tight_layout()
plt.show()

# 10. 性能统计
print(f'\n性能统计:')
print(f'平均位置误差: {np.mean(position_error):.6f}')
print(f'位置误差标准差: {np.std(position_error):.6f}')
print(f'速度MAE: {mean_absolute_error(testY_inverse[:, 2], predicted_inverse[:, 2]):.6f}')

# 11. 保存模型
model.save('enhanced_model.keras')
print('模型已保存为 enhanced_model.keras')
