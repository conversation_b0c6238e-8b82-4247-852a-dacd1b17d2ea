import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error
from keras.models import Sequential
from keras.layers import LSTM, Dense, Dropout, Activation, BatchNormalization
from keras.optimizers import Adam
from keras.callbacks import EarlyStopping, ReduceLROnPlateau
import os

plt.rcParams['font.sans-serif'] = ['SimHei']

class EnhancedTrajectoryPredictor:
    def __init__(self, time_step=5, use_all_features=True):
        """
        增强的轨迹预测器
        
        Args:
            time_step: 时间步长，用于构造序列数据
            use_all_features: 是否使用所有特征（经纬度+速度+方向），False则只使用经纬度
        """
        self.time_step = time_step
        self.use_all_features = use_all_features
        self.model = None
        self.scaler = None
        self.feature_names = ['lat', 'lon', 'speed', 'angle'] if use_all_features else ['lat', 'lon']
        self.n_features = len(self.feature_names)
        
    def load_data(self, file_path):
        """加载处理后的轨迹数据"""
        data = pd.read_csv(file_path, skiprows=1, header=None)
        data.columns = ['lat', 'lon', 'speed', 'angle']
        
        if self.use_all_features:
            # 使用所有特征
            selected_data = data[self.feature_names]
        else:
            # 只使用经纬度
            selected_data = data[['lat', 'lon']]
            
        print(f'数据加载完成 - 样本数：{len(selected_data)}, 特征维度：{selected_data.shape[1]}')
        print(f'使用特征：{self.feature_names}')
        return selected_data
    
    def preprocess_data(self, data):
        """数据预处理和归一化"""
        # 对不同特征使用不同的归一化策略
        if self.use_all_features:
            # 为经纬度、速度、角度分别设置归一化器
            self.scaler = {}
            processed_data = np.zeros_like(data.values)
            
            # 经纬度使用MinMaxScaler
            self.scaler['position'] = MinMaxScaler()
            processed_data[:, :2] = self.scaler['position'].fit_transform(data.iloc[:, :2])
            
            # 速度使用StandardScaler（因为速度可能有较大变化范围）
            self.scaler['speed'] = StandardScaler()
            processed_data[:, 2:3] = self.scaler['speed'].fit_transform(data.iloc[:, 2:3])
            
            # 角度需要特殊处理（0-360度的循环特征）
            # 将角度转换为sin和cos分量
            angles_rad = np.radians(data.iloc[:, 3].values)
            angle_sin = np.sin(angles_rad).reshape(-1, 1)
            angle_cos = np.cos(angles_rad).reshape(-1, 1)
            
            # 重新组织数据：lat, lon, speed, angle_sin, angle_cos
            processed_data = np.column_stack([
                processed_data[:, :3],  # lat, lon, speed
                angle_sin,              # sin(angle)
                angle_cos               # cos(angle)
            ])
            self.n_features = 5  # 更新特征数量
            
        else:
            # 只使用经纬度，简单的MinMaxScaler
            self.scaler = MinMaxScaler()
            processed_data = self.scaler.fit_transform(data)
            
        return processed_data
    
    def create_dataset(self, dataset):
        """构造时间序列数据集"""
        X, Y = [], []
        for i in range(len(dataset) - self.time_step):
            X.append(dataset[i:(i + self.time_step), :])
            if self.use_all_features:
                # 预测位置（lat, lon）和速度，不预测角度的sin/cos分量
                Y.append(dataset[i + self.time_step, :3])  # lat, lon, speed
            else:
                Y.append(dataset[i + self.time_step, :])   # lat, lon
        return np.array(X), np.array(Y)
    
    def build_model(self):
        """构建增强的LSTM模型"""
        model = Sequential()
        
        # 第一层LSTM
        model.add(LSTM(units=128, return_sequences=True, 
                      input_shape=(self.time_step, self.n_features)))
        model.add(BatchNormalization())
        model.add(Dropout(0.2))
        
        # 第二层LSTM
        model.add(LSTM(units=128, return_sequences=True))
        model.add(BatchNormalization())
        model.add(Dropout(0.2))
        
        # 第三层LSTM
        model.add(LSTM(units=64))
        model.add(BatchNormalization())
        model.add(Dropout(0.2))
        
        # 全连接层
        model.add(Dense(units=32, activation='relu'))
        model.add(Dropout(0.1))
        
        # 输出层
        if self.use_all_features:
            model.add(Dense(units=3))  # 预测 lat, lon, speed
        else:
            model.add(Dense(units=2))  # 预测 lat, lon
        model.add(Activation('linear'))
        
        # 编译模型
        model.compile(
            loss='mse', 
            optimizer=Adam(learning_rate=0.001), 
            metrics=['mae']
        )
        
        self.model = model
        return model
    
    def train(self, trainX, trainY, validation_split=0.2, epochs=100, batch_size=64):
        """训练模型"""
        # 设置回调函数
        callbacks = [
            EarlyStopping(monitor='val_loss', patience=15, restore_best_weights=True),
            ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=8, min_lr=1e-6)
        ]
        
        # 训练模型
        history = self.model.fit(
            trainX, trainY,
            epochs=epochs,
            batch_size=batch_size,
            validation_split=validation_split,
            callbacks=callbacks,
            verbose=1
        )
        
        return history
    
    def predict(self, testX):
        """进行预测"""
        return self.model.predict(testX)
    
    def inverse_transform_predictions(self, predictions, original_data=None):
        """反归一化预测结果"""
        if self.use_all_features:
            # 对于多特征情况，需要分别反归一化
            pred_positions = self.scaler['position'].inverse_transform(predictions[:, :2])
            pred_speeds = self.scaler['speed'].inverse_transform(predictions[:, 2:3])
            return np.column_stack([pred_positions, pred_speeds])
        else:
            return self.scaler.inverse_transform(predictions)
    
    def inverse_transform_targets(self, targets):
        """反归一化真实值"""
        if self.use_all_features:
            target_positions = self.scaler['position'].inverse_transform(targets[:, :2])
            target_speeds = self.scaler['speed'].inverse_transform(targets[:, 2:3])
            return np.column_stack([target_positions, target_speeds])
        else:
            return self.scaler.inverse_transform(targets)
    
    def evaluate_model(self, testX, testY):
        """评估模型性能"""
        predictions = self.predict(testX)
        
        # 反归一化
        pred_inverse = self.inverse_transform_predictions(predictions)
        test_inverse = self.inverse_transform_targets(testY)
        
        # 计算位置误差（经纬度）
        position_mae = mean_absolute_error(test_inverse[:, :2], pred_inverse[:, :2])
        position_mse = mean_squared_error(test_inverse[:, :2], pred_inverse[:, :2])
        
        # 计算欧氏距离误差
        euclidean_errors = np.sqrt(np.sum((test_inverse[:, :2] - pred_inverse[:, :2]) ** 2, axis=1))
        mean_euclidean_error = np.mean(euclidean_errors)
        
        print(f'位置预测 MAE: {position_mae:.6f}')
        print(f'位置预测 MSE: {position_mse:.6f}')
        print(f'平均欧氏距离误差: {mean_euclidean_error:.6f}')
        
        if self.use_all_features and pred_inverse.shape[1] > 2:
            # 计算速度误差
            speed_mae = mean_absolute_error(test_inverse[:, 2], pred_inverse[:, 2])
            speed_mse = mean_squared_error(test_inverse[:, 2], pred_inverse[:, 2])
            print(f'速度预测 MAE: {speed_mae:.6f}')
            print(f'速度预测 MSE: {speed_mse:.6f}')
        
        return {
            'position_mae': position_mae,
            'position_mse': position_mse,
            'euclidean_error': mean_euclidean_error,
            'euclidean_errors': euclidean_errors,
            'predictions': pred_inverse,
            'targets': test_inverse
        }
    
    def save_model(self, filepath):
        """保存模型"""
        self.model.save(filepath)
        print(f'模型已保存至: {filepath}')


def main():
    """主函数"""
    # 数据文件路径
    data_file = '../Processed/Data/001/Trajectory/20081024234405.csv'
    
    print("=" * 60)
    print("增强轨迹预测系统")
    print("=" * 60)
    
    # 创建两个预测器进行对比
    print("\n1. 训练基础模型（仅使用经纬度）...")
    basic_predictor = EnhancedTrajectoryPredictor(time_step=5, use_all_features=False)
    
    # 加载和预处理数据
    data_basic = basic_predictor.load_data(data_file)
    data_scaled_basic = basic_predictor.preprocess_data(data_basic)
    X_basic, Y_basic = basic_predictor.create_dataset(data_scaled_basic)
    
    # 划分训练集和测试集
    train_size = int(len(X_basic) * 0.8)
    trainX_basic, trainY_basic = X_basic[:train_size], Y_basic[:train_size]
    testX_basic, testY_basic = X_basic[train_size:], Y_basic[train_size:]
    
    print(f'基础模型 - Train X shape: {trainX_basic.shape}, Train Y shape: {trainY_basic.shape}')
    print(f'基础模型 - Test X shape: {testX_basic.shape}, Test Y shape: {testY_basic.shape}')
    
    # 构建和训练基础模型
    basic_predictor.build_model()
    basic_predictor.model.summary()
    history_basic = basic_predictor.train(trainX_basic, trainY_basic, epochs=50)
    
    print("\n2. 训练增强模型（使用经纬度+速度+方向）...")
    enhanced_predictor = EnhancedTrajectoryPredictor(time_step=5, use_all_features=True)
    
    # 加载和预处理数据
    data_enhanced = enhanced_predictor.load_data(data_file)
    data_scaled_enhanced = enhanced_predictor.preprocess_data(data_enhanced)
    X_enhanced, Y_enhanced = enhanced_predictor.create_dataset(data_scaled_enhanced)
    
    # 划分训练集和测试集
    train_size = int(len(X_enhanced) * 0.8)
    trainX_enhanced, trainY_enhanced = X_enhanced[:train_size], Y_enhanced[:train_size]
    testX_enhanced, testY_enhanced = X_enhanced[train_size:], Y_enhanced[train_size:]
    
    print(f'增强模型 - Train X shape: {trainX_enhanced.shape}, Train Y shape: {trainY_enhanced.shape}')
    print(f'增强模型 - Test X shape: {testX_enhanced.shape}, Test Y shape: {testY_enhanced.shape}')
    
    # 构建和训练增强模型
    enhanced_predictor.build_model()
    enhanced_predictor.model.summary()
    history_enhanced = enhanced_predictor.train(trainX_enhanced, trainY_enhanced, epochs=50)

    print("\n3. 模型评估和对比...")
    print("\n基础模型评估结果:")
    basic_results = basic_predictor.evaluate_model(testX_basic, testY_basic)

    print("\n增强模型评估结果:")
    enhanced_results = enhanced_predictor.evaluate_model(testX_enhanced, testY_enhanced)

    # 可视化结果
    visualize_results(basic_results, enhanced_results, history_basic, history_enhanced)

    # 保存模型
    basic_predictor.save_model('basic_model.keras')
    enhanced_predictor.save_model('enhanced_model.keras')

    print("\n=" * 60)
    print("训练完成！模型性能对比:")
    print(f"基础模型平均欧氏距离误差: {basic_results['euclidean_error']:.6f}")
    print(f"增强模型平均欧氏距离误差: {enhanced_results['euclidean_error']:.6f}")
    improvement = (basic_results['euclidean_error'] - enhanced_results['euclidean_error']) / basic_results['euclidean_error'] * 100
    print(f"性能提升: {improvement:.2f}%")
    print("=" * 60)


def visualize_results(basic_results, enhanced_results, history_basic, history_enhanced):
    """可视化训练结果和预测对比"""

    # 创建图形
    fig = plt.figure(figsize=(20, 12))

    # 1. 训练损失对比
    plt.subplot(2, 4, 1)
    plt.plot(history_basic.history['loss'], label='基础模型训练损失', alpha=0.7)
    plt.plot(history_basic.history['val_loss'], label='基础模型验证损失', alpha=0.7)
    plt.plot(history_enhanced.history['loss'], label='增强模型训练损失', alpha=0.7)
    plt.plot(history_enhanced.history['val_loss'], label='增强模型验证损失', alpha=0.7)
    plt.title('训练损失对比')
    plt.xlabel('Epochs')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 2. MAE对比
    plt.subplot(2, 4, 2)
    plt.plot(history_basic.history['mae'], label='基础模型训练MAE', alpha=0.7)
    plt.plot(history_basic.history['val_mae'], label='基础模型验证MAE', alpha=0.7)
    plt.plot(history_enhanced.history['mae'], label='增强模型训练MAE', alpha=0.7)
    plt.plot(history_enhanced.history['val_mae'], label='增强模型验证MAE', alpha=0.7)
    plt.title('MAE对比')
    plt.xlabel('Epochs')
    plt.ylabel('MAE')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 3. 轨迹预测对比 - 基础模型
    plt.subplot(2, 4, 3)
    basic_targets = basic_results['targets']
    basic_predictions = basic_results['predictions']
    plt.plot(basic_targets[:, 1], basic_targets[:, 0], 'b-', label='真实轨迹', linewidth=2, alpha=0.8)
    plt.plot(basic_predictions[:, 1], basic_predictions[:, 0], 'r--', label='预测轨迹', linewidth=2, alpha=0.8)
    plt.title('基础模型轨迹预测')
    plt.xlabel('经度')
    plt.ylabel('纬度')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 4. 轨迹预测对比 - 增强模型
    plt.subplot(2, 4, 4)
    enhanced_targets = enhanced_results['targets']
    enhanced_predictions = enhanced_results['predictions']
    plt.plot(enhanced_targets[:, 1], enhanced_targets[:, 0], 'b-', label='真实轨迹', linewidth=2, alpha=0.8)
    plt.plot(enhanced_predictions[:, 1], enhanced_predictions[:, 0], 'g--', label='预测轨迹', linewidth=2, alpha=0.8)
    plt.title('增强模型轨迹预测')
    plt.xlabel('经度')
    plt.ylabel('纬度')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 5. 误差分布对比
    plt.subplot(2, 4, 5)
    plt.hist(basic_results['euclidean_errors'], bins=30, alpha=0.7, label='基础模型', color='red')
    plt.hist(enhanced_results['euclidean_errors'], bins=30, alpha=0.7, label='增强模型', color='green')
    plt.title('预测误差分布')
    plt.xlabel('欧氏距离误差')
    plt.ylabel('频次')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 6. 误差时间序列
    plt.subplot(2, 4, 6)
    plt.plot(basic_results['euclidean_errors'], label='基础模型误差', alpha=0.7, color='red')
    plt.plot(enhanced_results['euclidean_errors'], label='增强模型误差', alpha=0.7, color='green')
    plt.title('预测误差时间序列')
    plt.xlabel('时间步')
    plt.ylabel('欧氏距离误差')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 7. 速度预测对比（仅增强模型）
    if enhanced_results['predictions'].shape[1] > 2:
        plt.subplot(2, 4, 7)
        plt.plot(enhanced_targets[:, 2], label='真实速度', alpha=0.8, color='blue')
        plt.plot(enhanced_predictions[:, 2], label='预测速度', alpha=0.8, color='orange')
        plt.title('速度预测对比（增强模型）')
        plt.xlabel('时间步')
        plt.ylabel('速度 (m/s)')
        plt.legend()
        plt.grid(True, alpha=0.3)

    # 8. 性能指标对比
    plt.subplot(2, 4, 8)
    metrics = ['位置MAE', '位置MSE', '欧氏距离误差']
    basic_values = [basic_results['position_mae'], basic_results['position_mse'], basic_results['euclidean_error']]
    enhanced_values = [enhanced_results['position_mae'], enhanced_results['position_mse'], enhanced_results['euclidean_error']]

    x = np.arange(len(metrics))
    width = 0.35

    plt.bar(x - width/2, basic_values, width, label='基础模型', alpha=0.8, color='red')
    plt.bar(x + width/2, enhanced_values, width, label='增强模型', alpha=0.8, color='green')

    plt.title('性能指标对比')
    plt.xlabel('指标')
    plt.ylabel('误差值')
    plt.xticks(x, metrics, rotation=45)
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('trajectory_prediction_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()


if __name__ == '__main__':
    main()
