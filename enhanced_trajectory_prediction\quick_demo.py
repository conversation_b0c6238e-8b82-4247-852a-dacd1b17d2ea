"""
快速演示脚本 - 增强轨迹预测
使用经纬度+速度+方向信息进行轨迹预测
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from enhanced_lstm import EnhancedTrajectoryPredictor

plt.rcParams['font.sans-serif'] = ['SimHei']

def quick_demo():
    """快速演示增强轨迹预测的效果"""
    
    print("=" * 50)
    print("快速演示：增强轨迹预测")
    print("=" * 50)
    
    # 数据文件路径
    data_file = '../Processed/Data/001/Trajectory/20081024234405.csv'
    
    # 创建增强预测器
    predictor = EnhancedTrajectoryPredictor(time_step=5, use_all_features=True)
    
    print("1. 加载数据...")
    data = predictor.load_data(data_file)
    print(f"数据概览:\n{data.head()}")
    print(f"数据统计:\n{data.describe()}")
    
    print("\n2. 数据预处理...")
    data_scaled = predictor.preprocess_data(data)
    print(f"预处理后数据形状: {data_scaled.shape}")
    
    print("\n3. 构造时间序列数据集...")
    X, Y = predictor.create_dataset(data_scaled)
    print(f"输入序列形状: {X.shape}")
    print(f"目标序列形状: {Y.shape}")
    
    # 划分训练集和测试集
    train_size = int(len(X) * 0.8)
    trainX, trainY = X[:train_size], Y[:train_size]
    testX, testY = X[train_size:], Y[train_size:]
    
    print(f"训练集: X={trainX.shape}, Y={trainY.shape}")
    print(f"测试集: X={testX.shape}, Y={testY.shape}")
    
    print("\n4. 构建和训练模型...")
    predictor.build_model()
    print("模型结构:")
    predictor.model.summary()
    
    # 训练模型（较少的epochs用于快速演示）
    print("\n开始训练...")
    history = predictor.train(trainX, trainY, epochs=30, batch_size=32)
    
    print("\n5. 模型评估...")
    results = predictor.evaluate_model(testX, testY)
    
    print("\n6. 可视化结果...")
    visualize_quick_results(results, history, data)
    
    print("\n7. 保存模型...")
    predictor.save_model('quick_demo_model.keras')
    
    print("\n演示完成！")
    return predictor, results


def visualize_quick_results(results, history, original_data):
    """快速可视化结果"""
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. 训练历史
    axes[0, 0].plot(history.history['loss'], label='训练损失')
    axes[0, 0].plot(history.history['val_loss'], label='验证损失')
    axes[0, 0].set_title('训练损失')
    axes[0, 0].set_xlabel('Epochs')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. MAE历史
    axes[0, 1].plot(history.history['mae'], label='训练MAE')
    axes[0, 1].plot(history.history['val_mae'], label='验证MAE')
    axes[0, 1].set_title('MAE变化')
    axes[0, 1].set_xlabel('Epochs')
    axes[0, 1].set_ylabel('MAE')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 轨迹对比
    targets = results['targets']
    predictions = results['predictions']
    
    axes[0, 2].plot(targets[:, 1], targets[:, 0], 'b-', label='真实轨迹', linewidth=2, alpha=0.8)
    axes[0, 2].plot(predictions[:, 1], predictions[:, 0], 'r--', label='预测轨迹', linewidth=2, alpha=0.8)
    axes[0, 2].set_title('轨迹预测对比')
    axes[0, 2].set_xlabel('经度')
    axes[0, 2].set_ylabel('纬度')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)
    
    # 4. 误差分布
    axes[1, 0].hist(results['euclidean_errors'], bins=30, alpha=0.7, color='green')
    axes[1, 0].set_title('预测误差分布')
    axes[1, 0].set_xlabel('欧氏距离误差')
    axes[1, 0].set_ylabel('频次')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 5. 误差时间序列
    axes[1, 1].plot(results['euclidean_errors'], color='red', alpha=0.7)
    axes[1, 1].set_title('预测误差时间序列')
    axes[1, 1].set_xlabel('时间步')
    axes[1, 1].set_ylabel('欧氏距离误差')
    axes[1, 1].grid(True, alpha=0.3)
    
    # 6. 速度预测对比
    if predictions.shape[1] > 2:
        axes[1, 2].plot(targets[:, 2], label='真实速度', alpha=0.8, color='blue')
        axes[1, 2].plot(predictions[:, 2], label='预测速度', alpha=0.8, color='orange')
        axes[1, 2].set_title('速度预测对比')
        axes[1, 2].set_xlabel('时间步')
        axes[1, 2].set_ylabel('速度 (m/s)')
        axes[1, 2].legend()
        axes[1, 2].grid(True, alpha=0.3)
    else:
        # 如果没有速度信息，显示原始数据统计
        axes[1, 2].plot(original_data['speed'].values[:500], alpha=0.7)
        axes[1, 2].set_title('原始速度数据（前500点）')
        axes[1, 2].set_xlabel('时间步')
        axes[1, 2].set_ylabel('速度 (m/s)')
        axes[1, 2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('quick_demo_results.png', dpi=300, bbox_inches='tight')
    plt.show()


def compare_with_basic_model():
    """与基础模型进行对比"""
    
    print("\n" + "=" * 50)
    print("模型对比：基础 vs 增强")
    print("=" * 50)
    
    data_file = '../Processed/Data/001/Trajectory/20081024234405.csv'
    
    # 基础模型（仅经纬度）
    print("训练基础模型...")
    basic_predictor = EnhancedTrajectoryPredictor(time_step=5, use_all_features=False)
    data_basic = basic_predictor.load_data(data_file)
    data_scaled_basic = basic_predictor.preprocess_data(data_basic)
    X_basic, Y_basic = basic_predictor.create_dataset(data_scaled_basic)
    
    train_size = int(len(X_basic) * 0.8)
    trainX_basic, trainY_basic = X_basic[:train_size], Y_basic[:train_size]
    testX_basic, testY_basic = X_basic[train_size:], Y_basic[train_size:]
    
    basic_predictor.build_model()
    basic_predictor.train(trainX_basic, trainY_basic, epochs=20)
    basic_results = basic_predictor.evaluate_model(testX_basic, testY_basic)
    
    # 增强模型（经纬度+速度+方向）
    print("\n训练增强模型...")
    enhanced_predictor = EnhancedTrajectoryPredictor(time_step=5, use_all_features=True)
    data_enhanced = enhanced_predictor.load_data(data_file)
    data_scaled_enhanced = enhanced_predictor.preprocess_data(data_enhanced)
    X_enhanced, Y_enhanced = enhanced_predictor.create_dataset(data_scaled_enhanced)
    
    train_size = int(len(X_enhanced) * 0.8)
    trainX_enhanced, trainY_enhanced = X_enhanced[:train_size], Y_enhanced[:train_size]
    testX_enhanced, testY_enhanced = X_enhanced[train_size:], Y_enhanced[train_size:]
    
    enhanced_predictor.build_model()
    enhanced_predictor.train(trainX_enhanced, trainY_enhanced, epochs=20)
    enhanced_results = enhanced_predictor.evaluate_model(testX_enhanced, testY_enhanced)
    
    # 对比结果
    print("\n" + "=" * 50)
    print("性能对比结果:")
    print("=" * 50)
    print(f"基础模型 - 平均欧氏距离误差: {basic_results['euclidean_error']:.6f}")
    print(f"增强模型 - 平均欧氏距离误差: {enhanced_results['euclidean_error']:.6f}")
    
    improvement = (basic_results['euclidean_error'] - enhanced_results['euclidean_error']) / basic_results['euclidean_error'] * 100
    print(f"性能提升: {improvement:.2f}%")
    
    if improvement > 0:
        print("✅ 增强模型性能更好！")
    else:
        print("⚠️ 基础模型性能更好，可能需要调整参数")
    
    return basic_results, enhanced_results


if __name__ == '__main__':
    # 运行快速演示
    predictor, results = quick_demo()
    
    # 可选：运行模型对比
    print("\n是否运行模型对比？(y/n): ", end="")
    choice = input().lower()
    if choice == 'y':
        basic_results, enhanced_results = compare_with_basic_model()
