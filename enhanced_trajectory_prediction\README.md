# 增强轨迹预测系统

这个项目是对原始轨迹预测系统的增强版本，通过加入速度和方向信息来提升预测精度。

## 主要改进

### 1. 多特征输入
- **原始版本**: 仅使用经纬度 (lat, lon)
- **增强版本**: 使用经纬度 + 速度 + 方向 (lat, lon, speed, angle)

### 2. 智能数据预处理
- 经纬度使用 MinMaxScaler 归一化
- 速度使用 StandardScaler 标准化
- 角度转换为 sin/cos 分量处理循环特征

### 3. 改进的模型架构
- 更深的LSTM网络（3层）
- 批量归一化（BatchNormalization）
- 改进的Dropout策略
- 早停和学习率调度

### 4. 全面的评估和可视化
- 训练过程可视化
- 轨迹预测对比
- 误差分析
- 性能指标对比

## 文件说明

### `enhanced_lstm.py`
主要的增强轨迹预测类，包含：
- `EnhancedTrajectoryPredictor`: 核心预测器类
- 完整的训练和评估流程
- 详细的可视化功能

### `quick_demo.py`
快速演示脚本，包含：
- 简化的使用示例
- 基础vs增强模型对比
- 快速可视化

## 使用方法

### 1. 快速开始
```python
# 运行快速演示
python quick_demo.py
```

### 2. 完整训练和对比
```python
# 运行完整的训练和对比
python enhanced_lstm.py
```

### 3. 自定义使用
```python
from enhanced_lstm import EnhancedTrajectoryPredictor

# 创建预测器
predictor = EnhancedTrajectoryPredictor(
    time_step=5,           # 时间步长
    use_all_features=True  # 是否使用所有特征
)

# 加载数据
data = predictor.load_data('path/to/your/data.csv')

# 预处理
data_scaled = predictor.preprocess_data(data)

# 构造数据集
X, Y = predictor.create_dataset(data_scaled)

# 训练模型
predictor.build_model()
history = predictor.train(X_train, Y_train)

# 评估
results = predictor.evaluate_model(X_test, Y_test)
```

## 数据格式要求

输入的CSV文件应包含以下列：
```
lat,lon,speed,angle
40.013812,116.306483,0.0,0.0
40.013763,116.306421,7.587254,224.100539
...
```

其中：
- `lat`: 纬度
- `lon`: 经度  
- `speed`: 速度 (m/s)
- `angle`: 方向角 (度，0-360)

## 主要特性

### 1. 灵活的特征选择
```python
# 仅使用经纬度
predictor = EnhancedTrajectoryPredictor(use_all_features=False)

# 使用所有特征
predictor = EnhancedTrajectoryPredictor(use_all_features=True)
```

### 2. 智能角度处理
角度特征被转换为sin和cos分量，避免了0°和360°之间的不连续性问题。

### 3. 多层次评估
- 位置预测误差 (MAE, MSE)
- 欧氏距离误差
- 速度预测误差（如果使用）
- 可视化对比

### 4. 模型保存和加载
```python
# 保存模型
predictor.save_model('my_model.keras')

# 加载模型（需要重新创建预测器实例）
# model = keras.models.load_model('my_model.keras')
```

## 预期改进效果

通过加入速度和方向信息，预期能够获得：
- 更准确的轨迹预测
- 更好的转弯点预测
- 更稳定的长期预测
- 更低的预测误差

## 依赖包

```
numpy
pandas
matplotlib
scikit-learn
tensorflow/keras
haversine (用于数据预处理)
```

## 注意事项

1. **数据质量**: 确保输入数据的速度和角度计算正确
2. **参数调优**: 可能需要根据具体数据调整网络参数
3. **计算资源**: 增强模型需要更多的计算资源和训练时间
4. **数据量**: 建议使用足够的训练数据以充分利用多特征优势

## 下一步改进建议

1. **加入更多特征**: 如加速度、时间间隔等
2. **注意力机制**: 在LSTM中加入注意力机制
3. **多任务学习**: 同时预测位置、速度和方向
4. **集成学习**: 结合多个模型的预测结果
5. **在线学习**: 支持增量学习和实时更新

## 问题排查

如果遇到问题，请检查：
1. 数据文件路径是否正确
2. 数据格式是否符合要求
3. 依赖包是否正确安装
4. 内存是否足够（特别是大数据集）
