# 增强轨迹预测

这是一个改进的轨迹预测系统，在原有经纬度基础上加入了速度和方向信息。

## 文件说明

- `enhanced_train.py` - 训练模型，使用经纬度+速度+方向信息
- `enhanced_test.py` - 测试模型，可以测试不同的轨迹文件

## 主要改进

1. **多特征输入**: 使用经纬度、速度、方向角度（转换为sin/cos分量）
2. **更深的网络**: 3层LSTM + 全连接层
3. **智能预处理**: 不同特征使用不同的归一化方法
4. **完整测试**: 包含未来轨迹预测功能

## 使用方法

1. 训练模型：
```bash
python enhanced_train.py
```

2. 测试模型：
```bash
python enhanced_test.py
```

## 输出文件

- `enhanced_model.keras` - 训练好的模型
- `scalers.pkl` - 归一化器（测试时需要）

## 数据格式

需要包含4列的CSV文件：lat, lon, speed, angle
