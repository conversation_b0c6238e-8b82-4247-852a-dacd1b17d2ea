import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_absolute_error
from keras.models import load_model
import pickle

plt.rcParams['font.sans-serif'] = ['SimHei']

# 1. 加载训练好的模型和归一化器
model = load_model('enhanced_model.keras')
with open('scalers.pkl', 'rb') as f:
    scalers = pickle.load(f)
    position_scaler = scalers['position_scaler']
    speed_scaler = scalers['speed_scaler']

print("模型和归一化器加载完成")

# 2. 读取测试数据（可以是不同的轨迹文件）
test_data = pd.read_csv('../Processed/Data/001/Trajectory/20081023055305.csv', skiprows=1, header=None)
test_data.columns = ['lat', 'lon', 'speed', 'angle']

print(f'测试数据样本数：{len(test_data)}, 维度：{test_data.shape[1]}')
print(f'测试数据概览:\n{test_data.head()}')

# 3. 数据预处理（与训练时相同）
# 对经纬度使用训练时的scaler
position_scaled = position_scaler.transform(test_data[['lat', 'lon']])

# 对速度使用训练时的scaler
speed_scaled = speed_scaler.transform(test_data[['speed']])

# 对角度进行特殊处理
angles_rad = np.radians(test_data['angle'].values)
angle_sin = np.sin(angles_rad).reshape(-1, 1)
angle_cos = np.cos(angles_rad).reshape(-1, 1)

# 合并所有特征
test_data_scaled = np.column_stack([position_scaled, speed_scaled, angle_sin, angle_cos])

# 4. 构造测试数据集
def create_dataset(dataset, time_step=5):
    X, Y = [], []
    for i in range(len(dataset) - time_step):
        X.append(dataset[i:(i + time_step), :])
        Y.append(dataset[i + time_step, :3])  # lat, lon, speed
    return np.array(X), np.array(Y)

time_step = 5
testX, testY = create_dataset(test_data_scaled, time_step)

print('Test X shape:', testX.shape)
print('Test Y shape:', testY.shape)

# 5. 进行预测
predicted = model.predict(testX)

# 6. 反归一化
predicted_positions = position_scaler.inverse_transform(predicted[:, :2])
predicted_speeds = speed_scaler.inverse_transform(predicted[:, 2:3])
predicted_inverse = np.column_stack([predicted_positions, predicted_speeds])

testY_positions = position_scaler.inverse_transform(testY[:, :2])
testY_speeds = speed_scaler.inverse_transform(testY[:, 2:3])
testY_inverse = np.column_stack([testY_positions, testY_speeds])

# 7. 计算误差
position_error = np.sqrt(np.sum((testY_inverse[:, :2] - predicted_inverse[:, :2]) ** 2, axis=1))
speed_error = np.abs(testY_inverse[:, 2] - predicted_inverse[:, 2])

# 8. 可视化测试结果
plt.figure(figsize=(15, 10))

# 轨迹对比
plt.subplot(2, 3, 1)
plt.plot(testY_inverse[:, 1], testY_inverse[:, 0], label='真实轨迹', marker='o', markersize=2, linestyle='-', color='blue')
plt.plot(predicted_inverse[:, 1], predicted_inverse[:, 0], label='预测轨迹', marker='x', markersize=2, linestyle='--', color='red')
plt.legend()
plt.title('测试轨迹：真实 vs 预测')
plt.xlabel('经度')
plt.ylabel('纬度')
plt.grid(True, alpha=0.3)

# 位置误差时间序列
plt.subplot(2, 3, 2)
plt.plot(position_error, color='red', alpha=0.7)
plt.title('位置预测误差')
plt.xlabel('时间步')
plt.ylabel('欧氏距离误差')
plt.grid(True, alpha=0.3)

# 速度对比
plt.subplot(2, 3, 3)
plt.plot(testY_inverse[:, 2], label='真实速度', alpha=0.8, color='blue')
plt.plot(predicted_inverse[:, 2], label='预测速度', alpha=0.8, color='orange')
plt.legend()
plt.title('速度预测对比')
plt.xlabel('时间步')
plt.ylabel('速度 (m/s)')
plt.grid(True, alpha=0.3)

# 位置误差分布
plt.subplot(2, 3, 4)
plt.hist(position_error, bins=30, alpha=0.7, color='red')
plt.title('位置误差分布')
plt.xlabel('欧氏距离误差')
plt.ylabel('频次')
plt.grid(True, alpha=0.3)

# 速度误差分布
plt.subplot(2, 3, 5)
plt.hist(speed_error, bins=30, alpha=0.7, color='orange')
plt.title('速度误差分布')
plt.xlabel('速度误差 (m/s)')
plt.ylabel('频次')
plt.grid(True, alpha=0.3)

# 误差散点图
plt.subplot(2, 3, 6)
plt.scatter(testY_inverse[:, 2], position_error, alpha=0.6, s=10)
plt.title('速度 vs 位置误差')
plt.xlabel('真实速度 (m/s)')
plt.ylabel('位置误差')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# 9. 性能统计
print(f'\n测试结果统计:')
print(f'平均位置误差: {np.mean(position_error):.6f}')
print(f'位置误差标准差: {np.std(position_error):.6f}')
print(f'最大位置误差: {np.max(position_error):.6f}')
print(f'平均速度误差: {np.mean(speed_error):.6f}')
print(f'速度误差标准差: {np.std(speed_error):.6f}')

# 10. 预测未来轨迹点（可选）
def predict_future_trajectory(model, last_sequence, n_steps=10):
    """预测未来n步的轨迹"""
    future_predictions = []
    current_sequence = last_sequence.copy()
    
    for _ in range(n_steps):
        # 预测下一个点
        next_pred = model.predict(current_sequence.reshape(1, time_step, -1))
        
        # 为了继续预测，需要构造完整的特征向量
        # 这里简化处理，假设角度保持不变
        next_full = np.zeros(5)
        next_full[:3] = next_pred[0]  # lat, lon, speed
        next_full[3:] = current_sequence[-1, 3:]  # 保持上一个角度的sin/cos
        
        future_predictions.append(next_pred[0])
        
        # 更新序列
        current_sequence = np.vstack([current_sequence[1:], next_full])
    
    return np.array(future_predictions)

# 使用最后一个序列预测未来轨迹
if len(testX) > 0:
    last_sequence = testX[-1]
    future_pred = predict_future_trajectory(model, last_sequence, n_steps=20)
    
    # 反归一化未来预测
    future_positions = position_scaler.inverse_transform(future_pred[:, :2])
    future_speeds = speed_scaler.inverse_transform(future_pred[:, 2:3])
    future_inverse = np.column_stack([future_positions, future_speeds])
    
    # 可视化未来预测
    plt.figure(figsize=(12, 8))
    
    plt.subplot(1, 2, 1)
    # 显示测试轨迹的最后部分
    plt.plot(testY_inverse[-50:, 1], testY_inverse[-50:, 0], 'b-', label='历史轨迹', linewidth=2)
    plt.plot(future_inverse[:, 1], future_inverse[:, 0], 'r--', label='未来预测', linewidth=2, marker='o', markersize=4)
    plt.legend()
    plt.title('未来轨迹预测')
    plt.xlabel('经度')
    plt.ylabel('纬度')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 2, 2)
    plt.plot(range(-50, 0), testY_inverse[-50:, 2], 'b-', label='历史速度', linewidth=2)
    plt.plot(range(0, 20), future_inverse[:, 2], 'r--', label='预测速度', linewidth=2, marker='o', markersize=4)
    plt.legend()
    plt.title('未来速度预测')
    plt.xlabel('时间步')
    plt.ylabel('速度 (m/s)')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    print(f'\n未来轨迹预测完成，预测了{len(future_inverse)}个点')

print('\n测试完成！')
